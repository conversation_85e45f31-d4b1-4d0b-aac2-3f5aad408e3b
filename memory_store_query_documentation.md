# Memory Store Query Method 技术文档

## 概述

`memory_store.query` 方法是一个基于双向量搜索和多维度评分的智能记忆检索系统。该方法通过结合内容向量、话题向量、重要性、可信度和时效性等多个维度，为查询文本找到最相关的记忆条目。

## 方法签名

```python
def query(self,
          query: str,
          k: int = 4,
          filters: Optional[Dict[str, Any]] = None,
          importance_weight: float = 0.2,
          confidence_weight: float = 0.2,
          recency_weight: float = 0.1,
          content_weights: float = 1.0,
          topic_weights: float = 1.0) -> List[Dict[str, Any]]
```

### 参数说明

- `query`: 查询文本字符串
- `k`: 返回结果的数量，默认为4
- `filters`: 可选的过滤条件字典，支持 `$ne`、`$in` 等操作符
- `importance_weight`: 重要性权重 (0-1)，默认0.2
- `confidence_weight`: 可信度权重 (0-1)，默认0.2
- `recency_weight`: 时效性权重 (0-1)，默认0.1
- `content_weights`: 内容向量权重 (0-1)，默认1.0
- `topic_weights`: 话题向量权重 (0-1)，默认1.0

## 核心算法流程

### 1. 向量嵌入生成

```python
raw_query_embedding = self.embeddings_model.embed_query(query)
query_embedding = self.normalize_vector(raw_query_embedding)
```

**算法细节：**
- 使用嵌入模型将查询文本转换为高维向量
- 对向量进行L2归一化，确保模长为1
- 归一化公式：`normalized_vector[i] = vector[i] / magnitude`
- 其中 `magnitude = sqrt(sum(x^2 for x in vector))`

**目的：** 归一化后的向量适用于余弦距离计算，提高相似度计算的准确性。

### 2. 双向量数据库搜索

#### 2.1 SQL查询构建

系统构建以下SQL查询来获取原始距离数据：

```sql
SELECT
    id, agent_id, domain, memory_type, content_type,
    topic, content,
    CASE
        WHEN importance = '' OR importance IS NULL THEN 0.1
        ELSE CAST(importance AS DECIMAL(10,2))
    END as importance,
    CASE
        WHEN confidence = '' OR confidence IS NULL THEN 0.3
        ELSE CAST(confidence AS DECIMAL(10,2))
    END as confidence,
    ttl, related_user_id, meta_data, created_at, updated_at,
    COALESCE(
        CASE WHEN embedding IS NOT NULL
             THEN vector_distance(embedding, :vector1, cosine)
             ELSE 2.0
        END,
        2.0
    ) as content_distance,
    COALESCE(
        CASE WHEN topic_embedding IS NOT NULL
             THEN vector_distance(topic_embedding, :vector2, cosine)
             ELSE 2.0
        END,
        2.0
    ) as topic_distance
FROM agent_memory
WHERE (embedding IS NOT NULL OR topic_embedding IS NOT NULL)
ORDER BY content_distance ASC
LIMIT :limit_k
```

#### 2.2 距离计算逻辑

- **向量距离函数**: 使用 `vector_distance(vector1, vector2, cosine)` 计算余弦距离
- **距离范围**: 0-2，其中0表示完全相同，2表示完全相反
- **空值处理**: 当向量为NULL时，距离设为最大值2.0作为惩罚
- **默认值处理**: importance默认0.1，confidence默认0.3

### 3. 多维度评分算法

#### 3.1 相似度得分计算

**加权距离计算：**
```python
total_weight = content_weights + topic_weights
weighted_distance = (content_distance * content_weights + topic_distance * topic_weights) / total_weight
```

**相似度转换：**
```python
similarity_score = 1.0 - (weighted_distance / 2.0)
similarity_score = max(0.0, min(1.0, similarity_score))
```

**算法特点：**
- 使用加权平均而非简单加权和
- 权重越小，对应维度的影响越小
- 最终得分范围：0-1，1表示完全相似

#### 3.2 重要性得分

```python
importance_score = memory["importance"] * importance_weight
```

- 直接将记忆的重要性值与权重相乘
- 得分范围：0 到 importance_weight

#### 3.3 可信度得分

```python
confidence_value = max(0.0, min(1.0, memory["confidence"]))
confidence_score = confidence_value * confidence_weight
```

- 首先将可信度值限制在[0,1]范围内
- 然后与权重相乘
- 得分范围：0 到 confidence_weight

#### 3.4 时效性得分

```python
days_diff = (now - created_at).days
if days_diff >= 365:
    recency_score = 0.0
else:
    recency_factor = 1.0 - (days_diff / 365.0)
    recency_score = recency_factor * recency_weight
```

**算法逻辑：**
- 计算记忆创建时间与当前时间的天数差
- 365天内线性衰减，超过365天得分为0
- 今天创建的记忆得分最高（recency_weight）
- 得分范围：0 到 recency_weight

### 4. 综合得分计算

```python
total_score = similarity_score + importance_score + confidence_score + recency_score
```

**评分体系特点：**
- 所有维度得分采用"越高越好"的统一标准
- 各维度得分直接相加，无额外权重调整
- 最终得分理论最大值：1 + importance_weight + confidence_weight + recency_weight

### 5. 结果排序与返回

```python
memories.sort(key=lambda x: x["score"], reverse=True)
final_results = memories[:k]
```

- 按综合得分降序排序
- 返回前k条结果
- 每条结果包含原始数据和所有计算得分

## 数据流图

```
查询文本
    ↓
向量嵌入 + 归一化
    ↓
双向量数据库搜索 (获取原始距离)
    ↓
多维度评分计算
    ├── 相似度得分 (基于加权向量距离)
    ├── 重要性得分
    ├── 可信度得分
    └── 时效性得分
    ↓
综合得分 = 各维度得分之和
    ↓
按得分排序 + 限制数量
    ↓
返回结果
```

## 权重系统设计

### 向量权重 (content_weights, topic_weights)

- **作用范围**: 仅影响相似度得分计算
- **计算方式**: 加权平均，避免权重和的影响
- **权重效果**: 权重越小，对应向量维度的影响越小
- **特殊情况**: 当两个权重都为0时，相似度得分为0

### 业务权重 (importance_weight, confidence_weight, recency_weight)

- **作用范围**: 分别影响对应维度的得分
- **计算方式**: 直接乘法
- **权重效果**: 权重越大，对应维度对最终排序的影响越大
- **取值范围**: 0-1，但理论上可以超过1

## 性能特征

### 时间复杂度

1. **向量嵌入**: O(d)，d为向量维度
2. **数据库搜索**: O(n log n)，n为候选记录数
3. **评分计算**: O(m)，m为返回记录数
4. **排序**: O(m log m)

### 空间复杂度

- **向量存储**: O(d)
- **结果存储**: O(m × f)，f为每条记录的字段数

## 错误处理

1. **嵌入模型未设置**: 抛出 `ValueError`
2. **数据库连接失败**: 返回空列表，记录错误日志
3. **向量搜索失败**: 返回空列表，发送告警
4. **空值处理**: 使用预设默认值

## 扩展性考虑

### 新增评分维度

要添加新的评分维度，需要：
1. 在 `_calculate_weighted_scores` 方法中添加新的得分计算
2. 在综合得分公式中包含新维度
3. 在方法签名中添加对应的权重参数

### 向量维度扩展

系统支持任意维度的向量，只需确保：
1. 嵌入模型输出维度一致
2. 数据库向量列支持对应维度
3. 归一化算法保持不变

## 使用示例

```python
# 基础查询
results = memory_store.query("如何学习Python")

# 自定义权重查询
results = memory_store.query(
    query="机器学习算法",
    k=10,
    content_weights=0.8,  # 降低内容权重
    topic_weights=1.0,    # 保持话题权重
    importance_weight=0.5, # 提高重要性影响
    filters={"domain": "技术"}
)
```

## 算法优势

1. **多维度评估**: 结合语义相似度和业务属性
2. **灵活权重控制**: 支持细粒度的权重调整
3. **高效搜索**: 数据库层面的向量搜索优化
4. **可扩展性**: 易于添加新的评分维度
5. **鲁棒性**: 完善的错误处理和默认值机制
